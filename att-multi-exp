//@version=5
indicator("Advanced Time Technique (ATT) - Dynamic 60-Candle Boxes", overlay=true, max_labels_count=500, max_boxes_count=500)

// ATT Box Settings
group_att = 'ATT Box Settings'
showATTBoxes     = input.bool(true, 'Show ATT Boxes', group=group_att)
attBoxCount      = input.int(3, 'Number of ATT Boxes', minval=1, maxval=10, group=group_att)
bullC            = input.color(#26a69a, 'Box : Bull', inline='COLORS', group=group_att)
bearC            = input.color(#ef5350, 'Bear', inline='COLORS', group=group_att)
trans            = input.int(85, 'Transparency', inline='STYLE', minval=65, maxval=95, group=group_att)
lw               = input.int(1, 'Line Width', inline='STYLE', minval=1, maxval=4, group=group_att)
extendBoxes      = input.bool(true, 'Extend Boxes Right', group=group_att)

// ATT Numbers Settings
group_numbers = 'ATT Numbers Settings'
showNumbers      = input.bool(true, 'Show ATT Numbers', group=group_numbers)
numbersColor     = input.color(color.white, 'Numbers Color', inline='NUM_STYLE', group=group_numbers)
numbersSize      = input.string(size.small, 'Numbers Size', options=[size.tiny, size.small, size.normal], inline='NUM_STYLE', group=group_numbers)
arrowStyle       = input.string('Arrow Down', 'Arrow Style', options=['Arrow Down', 'Arrow Up', 'Triangle Down', 'Triangle Up'], group=group_numbers)

// ATT Box Info Settings
group_info = 'ATT Box Info'
showBoxInfo      = input.bool(false, 'Show Box Info Labels', group=group_info)
showBoxNumbers   = input.bool(true, 'Show Box Numbers', group=group_info)
infoColor        = input.color(color.yellow, 'Info Color', group=group_info)

// The ATT candle numbers where arrow marks will be drawn
var att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

// Get current timeframe in minutes for calculations
getCurrentTFMinutes() =>
    tf = timeframe.period
    minutes = 0
    if str.contains(tf, "S")
        minutes := str.tonumber(str.replace(tf, "S", "")) / 60
    else if str.contains(tf, "D")
        minutes := str.tonumber(str.replace(tf, "D", "")) * 1440
    else if str.contains(tf, "W")
        minutes := str.tonumber(str.replace(tf, "W", "")) * 10080
    else if str.contains(tf, "M")
        minutes := str.tonumber(str.replace(tf, "M", "")) * 43200
    else
        minutes := str.tonumber(tf)
    minutes

// Calculate ATT box period (60 candles of current timeframe)
getATTBoxPeriod() =>
    currentTFMinutes = getCurrentTFMinutes()
    attBoxMinutes = currentTFMinutes * 60  // 60 candles of current timeframe

    // Convert back to timeframe string
    if attBoxMinutes < 60
        str.tostring(math.round(attBoxMinutes * 60)) + "S"
    else if attBoxMinutes < 1440
        str.tostring(math.round(attBoxMinutes))
    else if attBoxMinutes < 10080
        str.tostring(math.round(attBoxMinutes / 1440)) + "D"
    else if attBoxMinutes < 43200
        str.tostring(math.round(attBoxMinutes / 10080)) + "W"
    else
        str.tostring(math.round(attBoxMinutes / 43200)) + "M"

// ATT Box Data Structure
type ATTBoxData
    float high
    float low
    float open
    float close
    int startBar
    int endBar
    int candleCount
    bool isComplete

// Rolling 60-candle window tracker
var array<ATTBoxData> attBoxes = array.new<ATTBoxData>()
var int currentCandleInPeriod = 0
var float periodHigh = na
var float periodLow = na
var float periodOpen = na
var int periodStartBar = na

// Initialize or update current ATT period
updateATTPeriod() =>
    // Reset every 60 candles
    if currentCandleInPeriod == 0
        periodHigh := high
        periodLow := low
        periodOpen := open
        periodStartBar := bar_index
    else
        periodHigh := math.max(periodHigh, high)
        periodLow := math.min(periodLow, low)

    currentCandleInPeriod += 1

    // Complete the period after 60 candles
    if currentCandleInPeriod >= 60
        // Create completed ATT box
        newBox = ATTBoxData.new(
            high = periodHigh,
            low = periodLow,
            open = periodOpen,
            close = close[1],  // Previous candle close
            startBar = periodStartBar,
            endBar = bar_index - 1,
            candleCount = 60,
            isComplete = true
        )

        // Add to array and limit size
        array.unshift(attBoxes, newBox)
        if array.size(attBoxes) > attBoxCount
            array.pop(attBoxes)

        // Reset for next period
        currentCandleInPeriod := 0

    [periodHigh, periodLow, periodOpen, currentCandleInPeriod]

// Get arrow label style based on user selection
getArrowStyle() =>
    switch arrowStyle
        'Arrow Down' => label.style_arrowdown
        'Arrow Up' => label.style_arrowup
        'Triangle Down' => label.style_triangledown
        'Triangle Up' => label.style_triangleup
        => label.style_arrowdown

// Global arrays for managing visual elements
var array<box> attBoxArray = array.new<box>()
var array<label> attLabelArray = array.new<label>()

// Draw ATT boxes for completed periods
drawATTBoxes() =>
    if showATTBoxes and array.size(attBoxes) > 0
        // Clear existing boxes
        for i = 0 to array.size(attBoxArray) - 1
            box.delete(array.get(attBoxArray, i))
        array.clear(attBoxArray)

        // Draw boxes for each completed ATT period
        for i = 0 to math.min(array.size(attBoxes) - 1, attBoxCount - 1)
            boxData = array.get(attBoxes, i)
            if not na(boxData)
                // Determine box color based on open vs close
                boxColor = boxData.open < boxData.close ?
                    color.new(bullC, trans) : color.new(bearC, trans)
                borderColor = boxData.open < boxData.close ?
                    color.new(bullC, trans/2) : color.new(bearC, trans/2)

                // Create box
                extendType = extendBoxes ? extend.right : extend.none
                newBox = box.new(
                    left = boxData.startBar,
                    top = boxData.high,
                    right = boxData.endBar,
                    bottom = boxData.low,
                    border_color = borderColor,
                    border_width = lw,
                    border_style = line.style_solid,
                    extend = extendType,
                    xloc = xloc.bar_index,
                    bgcolor = boxColor
                )
                array.push(attBoxArray, newBox)

                // Add box info label if enabled
                if showBoxInfo or showBoxNumbers
                    labelText = ""
                    if showBoxNumbers
                        labelText += "ATT " + str.tostring(i + 1)
                    if showBoxInfo
                        if showBoxNumbers
                            labelText += "\n"
                        labelText += "H: " + str.tostring(boxData.high, "#.####") +
                                   " L: " + str.tostring(boxData.low, "#.####")

                    if labelText != ""
                        infoLabel = label.new(
                            x = boxData.startBar,
                            y = boxData.high,
                            text = labelText,
                            style = label.style_label_down,
                            color = color.new(infoColor, 20),
                            textcolor = color.white,
                            size = size.small
                        )

// Draw current period box (incomplete)
drawCurrentPeriodBox() =>
    if showATTBoxes and currentCandleInPeriod > 0 and not na(periodHigh) and not na(periodLow)
        // Determine current box color
        currentBoxColor = periodOpen < close ?
            color.new(bullC, trans + 10) : color.new(bearC, trans + 10)
        currentBorderColor = periodOpen < close ?
            color.new(bullC, trans/2 + 10) : color.new(bearC, trans/2 + 10)

        // Create current period box
        var box currentBox = na
        box.delete(currentBox)
        extendType = extendBoxes ? extend.right : extend.none
        currentBox := box.new(
            left = periodStartBar,
            top = periodHigh,
            right = bar_index,
            bottom = periodLow,
            border_color = currentBorderColor,
            border_width = lw,
            border_style = line.style_dashed,
            extend = extendType,
            xloc = xloc.bar_index,
            bgcolor = currentBoxColor
        )

// Draw ATT number arrows
drawATTNumbers() =>
    if showNumbers and array.includes(att_numbers, currentCandleInPeriod)
        arrowPos = high + (high - low) * 0.1
        arrowLabel = label.new(
            x = bar_index,
            y = arrowPos,
            text = str.tostring(currentCandleInPeriod),
            style = getArrowStyle(),
            textcolor = numbersColor,
            size = numbersSize,
            yloc = yloc.price,
            color = color.new(numbersColor, 80),
            textalign = text.align_center
        )
        array.push(attLabelArray, arrowLabel)

        // Cleanup old labels
        if array.size(attLabelArray) > 100
            label.delete(array.shift(attLabelArray))

// ------------------------- Main Logic ------------------------- //

// Update ATT period tracking
[currentHigh, currentLow, currentOpen, candleInPeriod] = updateATTPeriod()

// Draw all visual elements
if chart.is_standard
    drawATTBoxes()
    drawCurrentPeriodBox()
    drawATTNumbers()

// Display current ATT info in a table (optional)
if showBoxInfo
    var table infoTable = table.new(position.top_right, 2, 4, bgcolor=color.new(color.black, 80), border_width=1)
    if barstate.islast
        table.cell(infoTable, 0, 0, "ATT Info", text_color=color.white, text_size=size.small)
        table.cell(infoTable, 1, 0, "", text_color=color.white, text_size=size.small)

        currentTF = getCurrentTFMinutes()
        attPeriodHours = currentTF * 60 / 60  // Convert to hours

        table.cell(infoTable, 0, 1, "Chart TF:", text_color=color.white, text_size=size.tiny)
        table.cell(infoTable, 1, 1, timeframe.period, text_color=color.yellow, text_size=size.tiny)

        table.cell(infoTable, 0, 2, "ATT Period:", text_color=color.white, text_size=size.tiny)
        table.cell(infoTable, 1, 2, str.tostring(attPeriodHours) + "h", text_color=color.yellow, text_size=size.tiny)

        table.cell(infoTable, 0, 3, "Candle #:", text_color=color.white, text_size=size.tiny)
        table.cell(infoTable, 1, 3, str.tostring(currentCandleInPeriod) + "/60", text_color=color.yellow, text_size=size.tiny)
