//@version=5
indicator("Advanced Time Technique (ATT) - Dynamic 60-Candle Boxes", overlay=true, max_labels_count=500, max_boxes_count=500)

// ATT Box Settings
group_att = 'ATT Box Settings'
showATTBoxes     = input.bool(true, 'Show ATT Boxes', group=group_att)
attBoxCount      = input.int(3, 'Number of ATT Boxes', minval=1, maxval=10, group=group_att)
bullC            = input.color(#26a69a, 'Box : Bull', inline='COLORS', group=group_att)
bearC            = input.color(#ef5350, 'Bear', inline='COLORS', group=group_att)
trans            = input.int(85, 'Transparency', inline='STYLE', minval=65, maxval=95, group=group_att)
lw               = input.int(1, 'Line Width', inline='STYLE', minval=1, maxval=4, group=group_att)
extendBoxes      = input.bool(true, 'Extend Boxes Right', group=group_att)

// ATT Numbers Settings
group_numbers = 'ATT Numbers Settings'
showNumbers      = input.bool(true, 'Show ATT Numbers', group=group_numbers)
numbersColor     = input.color(color.white, 'Numbers Color', inline='NUM_STYLE', group=group_numbers)
numbersSize      = input.string(size.small, 'Numbers Size', options=[size.tiny, size.small, size.normal], inline='NUM_STYLE', group=group_numbers)
arrowStyle       = input.string('Arrow Down', 'Arrow Style', options=['Arrow Down', 'Arrow Up', 'Triangle Down', 'Triangle Up'], group=group_numbers)

// ATT Box Info Settings
group_info = 'ATT Box Info'
showBoxInfo      = input.bool(false, 'Show Box Info Labels', group=group_info)
showBoxNumbers   = input.bool(true, 'Show Box Numbers', group=group_info)
infoColor        = input.color(color.yellow, 'Info Color', group=group_info)

// The ATT candle numbers where arrow marks will be drawn
var att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

// Get current timeframe in minutes for calculations
getCurrentTFMinutes() =>
    tf = timeframe.period
    minutes = 0
    if str.contains(tf, "S")
        minutes := str.tonumber(str.replace(tf, "S", "")) / 60
    else if str.contains(tf, "D")
        minutes := str.tonumber(str.replace(tf, "D", "")) * 1440
    else if str.contains(tf, "W")
        minutes := str.tonumber(str.replace(tf, "W", "")) * 10080
    else if str.contains(tf, "M")
        minutes := str.tonumber(str.replace(tf, "M", "")) * 43200
    else
        minutes := str.tonumber(tf)
    minutes

// Calculate ATT box period (60 candles of current timeframe)
getATTBoxPeriod() =>
    currentTFMinutes = getCurrentTFMinutes()
    attBoxMinutes = currentTFMinutes * 60  // 60 candles of current timeframe

    // Convert back to timeframe string
    if attBoxMinutes < 60
        str.tostring(math.round(attBoxMinutes * 60)) + "S"
    else if attBoxMinutes < 1440
        str.tostring(math.round(attBoxMinutes))
    else if attBoxMinutes < 10080
        str.tostring(math.round(attBoxMinutes / 1440)) + "D"
    else if attBoxMinutes < 43200
        str.tostring(math.round(attBoxMinutes / 10080)) + "W"
    else
        str.tostring(math.round(attBoxMinutes / 43200)) + "M"

// Original HTF OHLC function adapted for 60-candle ATT periods
f_htf_ohlc_att() =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0., htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.
    var int candleCount = 0

    // Reset every 60 candles instead of using timeframe change
    if candleCount == 0
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        candleCount := 1
        true
    else if candleCount >= 60
        // Complete the 60-candle period and start new one
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        candleCount := 1
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        candleCount += 1
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c, candleCount]

// Global label arrays (keeping original structure)
var label[] candleLabelsATT = array.new_label()

// Original f_processCandles function adapted for 60-candle ATT periods
f_processCandles(_show, _bullC, _bearC, _trans, _width, _labelArr) =>
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc_att()

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        // Use 60-candle periods instead of timeframe change
        if candleCount == 1  // New 60-candle period started
            x11 := x1
            x1  := bar_index

            if L1 != 0
                extendType = extendBoxes ? extend.right : extend.none
                box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extendType, xloc.bar_index, color1)
                box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extendType, xloc.bar_index, color1)

            extendTypeCurrent = extendBoxes ? extend.right : extend.none
            box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extendTypeCurrent, xloc.bar_index, color0)
            box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extendTypeCurrent, xloc.bar_index, color0)

        else
            box.set_top(hl, H0)
            box.set_bottom(hl, L0)
            box.set_bgcolor(hl, color0)
            box.set_border_color(hl, color01)

            box.set_top(oc, math.max(O0, C0))
            box.set_bottom(oc, math.min(O0, C0))
            box.set_bgcolor(oc, color0)
            box.set_border_color(oc, color01)

        // Draw arrow mark for specific candle numbers (ATT numbers)
        if showNumbers and array.includes(att_numbers, candleCount)
            arrowPos = high + (high - low) * 0.1
            arrowStyle = switch arrowStyle
                'Arrow Down' => label.style_arrowdown
                'Arrow Up' => label.style_arrowup
                'Triangle Down' => label.style_triangledown
                'Triangle Up' => label.style_triangleup
                => label.style_arrowdown

            attLabel = label.new(bar_index, arrowPos, str.tostring(candleCount), style=arrowStyle, textcolor=numbersColor, size=numbersSize, yloc=yloc.price, color=color.new(numbersColor, 80), textalign=text.align_center)
            array.push(_labelArr, attLabel)

        // Cleanup labels to prevent overload
        if array.size(_labelArr) > 100
            label.delete(array.shift(_labelArr))

        // Add box info if enabled
        if (showBoxInfo or showBoxNumbers) and candleCount == 1 and L1 != 0
            labelText = ""
            if showBoxNumbers
                labelText += "ATT 60"
            if showBoxInfo
                if showBoxNumbers
                    labelText += "\n"
                currentTF = getCurrentTFMinutes()
                attPeriodHours = currentTF * 60 / 60
                labelText += str.tostring(attPeriodHours) + "h Box"

            if labelText != ""
                infoLabel = label.new(x = x11,y = H1,text = labelText,style = label.style_label_down,color = color.new(infoColor, 20),textcolor = color.white,size = size.small)

// ------------------------- Main Logic ------------------------- //

// Process ATT candles using original box drawing logic
if chart.is_standard
    f_processCandles(showATTBoxes, bullC, bearC, trans, lw, candleLabelsATT)

// Display current ATT info in a table (optional)
if showBoxInfo
    var table infoTable = table.new(position.top_right, 2, 4, bgcolor=color.new(color.black, 80), border_width=1)
    if barstate.islast
        table.cell(infoTable, 0, 0, "ATT Info", text_color=color.white, text_size=size.small)
        table.cell(infoTable, 1, 0, "", text_color=color.white, text_size=size.small)

        currentTF = getCurrentTFMinutes()
        attPeriodHours = currentTF * 60 / 60  // Convert to hours

        table.cell(infoTable, 0, 1, "Chart TF:", text_color=color.white, text_size=size.tiny)
        table.cell(infoTable, 1, 1, timeframe.period, text_color=color.yellow, text_size=size.tiny)

        table.cell(infoTable, 0, 2, "ATT Period:", text_color=color.white, text_size=size.tiny)
        table.cell(infoTable, 1, 2, str.tostring(attPeriodHours) + "h", text_color=color.yellow, text_size=size.tiny)

        // Get current candle count from the function
        [_, _, _, _, _, _, _, _, currentCandle] = f_htf_ohlc_att()
        table.cell(infoTable, 0, 3, "Candle #:", text_color=color.white, text_size=size.tiny)
        table.cell(infoTable, 1, 3, str.tostring(currentCandle) + "/60", text_color=color.yellow, text_size=size.tiny)
