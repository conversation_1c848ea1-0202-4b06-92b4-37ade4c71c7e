//@version=5
indicator('Fixed Range Volume Profile - Previous Day', 'FRVP-PD', overlay=true, max_boxes_count=500, max_bars_back=5000)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// INPUTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Profile Settings
lookback_bars = input.int(title='Lookback Bars', defval=1440, minval=100, maxval=5000, tooltip="Number of bars to look back (1440 = ~1 day on 1min chart)")
row_size = input.int(title='Row Size', defval=50, minval=10, maxval=100, tooltip="Number of price levels in the volume profile")
value_area_percent = input.float(70.0, title='Value Area Volume %', minval=50.0, maxval=90.0, step=5.0)

// Key Levels Display
show_poc = input.bool(true, "Show POC", tooltip="Show Point of Control", group="Key Levels")
show_vah = input.bool(true, "Show VAH", tooltip="Show Value Area High", group="Key Levels")
show_val = input.bool(true, "Show VAL", tooltip="Show Value Area Low", group="Key Levels")
show_poc_label = input.bool(true, "Show POC Label", tooltip="Show POC price label", group="Key Levels")
extend_levels = input.bool(true, "Extend Lines", tooltip="Extend key level lines to the right", group="Key Levels")

// Appearance Settings
poc_color = input.color(color.white, title='POC Color', group="Appearance")
poc_width = input.int(2, title='POC Width', minval=1, maxval=5, group="Appearance")
vah_color = input.color(color.red, title='VAH Color', group="Appearance")
val_color = input.color(color.green, title='VAL Color', group="Appearance")
value_area_up_color = input.color(color.new(color.blue, 30), title='Value Area Up Volume', group="Appearance")
value_area_down_color = input.color(color.new(color.orange, 30), title='Value Area Down Volume', group="Appearance")
up_volume_color = input.color(color.new(color.blue, 75), title='UP Volume', group="Appearance")
down_volume_color = input.color(color.new(color.orange, 75), title='Down Volume', group="Appearance")

// Profile Position
profile_width_bars = input.int(100, "Profile Width (bars)", minval=20, maxval=200, tooltip="Width of volume profile in bars", group="Position")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Function to get previous day's high and low
get_prev_day_levels() =>
    prev_high = request.security(syminfo.tickerid, "D", high[1], barmerge.gaps_off, barmerge.lookahead_off)
    prev_low = request.security(syminfo.tickerid, "D", low[1], barmerge.gaps_off, barmerge.lookahead_off)
    [prev_high, prev_low]

// Function to get volume intersection (from reference code)
get_vol(y11, y12, y21, y22, height, vol) =>
    nz(math.max(math.min(math.max(y11, y12), math.max(y21, y22)) - math.max(math.min(y11, y12), math.min(y21, y22)), 0) * vol / height)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// MAIN LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Get previous day's high and low
[prev_day_high, prev_day_low] = get_prev_day_levels()

// Variables to store previous day levels
var float stored_prev_high = na
var float stored_prev_low = na

// Detect new day and store previous day levels
is_new_day = ta.change(time("D")) != 0

if is_new_day and not na(prev_day_high) and not na(prev_day_low)
    stored_prev_high := prev_day_high
    stored_prev_low := prev_day_low

// Calculate and draw volume profile on last bar
if barstate.islast and not na(stored_prev_high) and not na(stored_prev_low) and stored_prev_high > stored_prev_low
    
    // Use stored previous day high/low as range
    top = stored_prev_high
    bot = stored_prev_low
    dist = (top - bot) / 500
    step = (top - bot) / row_size

    // Calculate/keep channel levels
    levels = array.new_float(row_size + 1)
    for x = 0 to row_size
        array.set(levels, x, bot + step * x)

    // Calculate/get volume for each channel and candle
    volumes = array.new_float(row_size * 2, 0.)
    
    for bars = 0 to lookback_bars - 1
        if bars <= bar_index
            body_top = math.max(close[bars], open[bars])
            body_bot = math.min(close[bars], open[bars])
            itsgreen = close[bars] >= open[bars]

            topwick = high[bars] - body_top
            bottomwick = body_bot - low[bars]
            body = body_top - body_bot

            // Avoid division by zero
            total_range = 2 * topwick + 2 * bottomwick + body
            if total_range > 0
                bodyvol = body * volume[bars] / total_range
                topwickvol = 2 * topwick * volume[bars] / total_range
                bottomwickvol = 2 * bottomwick * volume[bars] / total_range
                
                for x = 0 to row_size - 1
                    // Up volume (green candles body + wicks)
                    up_vol = (itsgreen ? get_vol(array.get(levels, x), array.get(levels, x + 1), body_bot, body_top, body, bodyvol) : 0) + 
                             get_vol(array.get(levels, x), array.get(levels, x + 1), body_top, high[bars], topwick, topwickvol) / 2 + 
                             get_vol(array.get(levels, x), array.get(levels, x + 1), body_bot, low[bars], bottomwick, bottomwickvol) / 2
                    
                    // Down volume (red candles body + wicks)
                    down_vol = (itsgreen ? 0 : get_vol(array.get(levels, x), array.get(levels, x + 1), body_bot, body_top, body, bodyvol)) + 
                               get_vol(array.get(levels, x), array.get(levels, x + 1), body_top, high[bars], topwick, topwickvol) / 2 + 
                               get_vol(array.get(levels, x), array.get(levels, x + 1), body_bot, low[bars], bottomwick, bottomwickvol) / 2
                    
                    array.set(volumes, x, array.get(volumes, x) + up_vol)
                    array.set(volumes, x + row_size, array.get(volumes, x + row_size) + down_vol)

    // Calculate total volumes for each level
    totalvols = array.new_float(row_size, 0.)
    for x = 0 to row_size - 1
        array.set(totalvols, x, array.get(volumes, x) + array.get(volumes, x + row_size))

    // Find POC (Point of Control)
    int poc = array.indexof(totalvols, array.max(totalvols))

    // Calculate value area
    totalmax = array.sum(totalvols) * value_area_percent / 100.
    va_total = array.get(totalvols, poc)
    int up = poc
    int down = poc
    
    for x = 0 to row_size - 1
        if va_total >= totalmax
            break
        uppervol = up < row_size - 1 ? array.get(totalvols, up + 1) : 0.
        lowervol = down > 0 ? array.get(totalvols, down - 1) : 0.
        if uppervol == 0 and lowervol == 0
            break
        if uppervol >= lowervol
            va_total += uppervol
            up += 1
        else
            va_total += lowervol
            down -= 1

    // Scale volumes for display
    maxvol = array.max(totalvols)
    if maxvol > 0
        for x = 0 to row_size * 2 - 1
            array.set(volumes, x, array.get(volumes, x) * profile_width_bars / (3 * maxvol))

    // Draw VP rows
    var vol_bars = array.new_box(row_size * 2, na)
    for x = 0 to row_size - 1
        box.delete(array.get(vol_bars, x))
        box.delete(array.get(vol_bars, x + row_size))
        
        // Up volume bars
        array.set(vol_bars, x, box.new(bar_index - lookback_bars + 1, 
                                       array.get(levels, x + 1) - dist, 
                                       bar_index - lookback_bars + 1 + math.round(array.get(volumes, x)), 
                                       array.get(levels, x) + dist, 
                                       border_width=0, 
                                       bgcolor=x >= down and x <= up ? value_area_up_color : up_volume_color))
        
        // Down volume bars
        array.set(vol_bars, x + row_size, box.new(bar_index - lookback_bars + 1 + math.round(array.get(volumes, x)), 
                                              array.get(levels, x + 1) - dist, 
                                              bar_index - lookback_bars + 1 + math.round(array.get(volumes, x)) + math.round(array.get(volumes, x + row_size)), 
                                              array.get(levels, x) + dist, 
                                              border_width=0, 
                                              bgcolor=x >= down and x <= up ? value_area_down_color : down_volume_color))

    // Calculate key price levels
    poc_level = (array.get(levels, poc) + array.get(levels, poc + 1)) / 2
    vah_level = (array.get(levels, up) + array.get(levels, up + 1)) / 2
    val_level = (array.get(levels, down) + array.get(levels, down + 1)) / 2

    // Draw POC line
    if show_poc
        var line poc_line = na
        line.delete(poc_line)
        line_end = extend_levels ? bar_index + 100 : bar_index - lookback_bars + profile_width_bars + 10
        poc_line := line.new(bar_index - lookback_bars + 1, poc_level, line_end, poc_level, 
                            extend=extend_levels ? extend.right : extend.none, 
                            color=poc_color, width=poc_width)

    // Draw VAH line
    if show_vah
        var line vah_line = na
        line.delete(vah_line)
        line_end = extend_levels ? bar_index + 100 : bar_index - lookback_bars + profile_width_bars + 10
        vah_line := line.new(bar_index - lookback_bars + 1, vah_level, line_end, vah_level, 
                            extend=extend_levels ? extend.right : extend.none, 
                            color=vah_color, width=1, style=line.style_dashed)

    // Draw VAL line
    if show_val
        var line val_line = na
        line.delete(val_line)
        line_end = extend_levels ? bar_index + 100 : bar_index - lookback_bars + profile_width_bars + 10
        val_line := line.new(bar_index - lookback_bars + 1, val_level, line_end, val_level, 
                            extend=extend_levels ? extend.right : extend.none, 
                            color=val_color, width=1, style=line.style_dashed)

    // Draw POC label
    if show_poc_label
        var label poc_label = na
        label.delete(poc_label)
        poc_label := label.new(bar_index + 15, poc_level, 
                               text = "POC: " + str.tostring(math.round_to_mintick(poc_level)),
                               style = close >= poc_level ? label.style_label_up : label.style_label_down,
                               color = poc_color,
                               textcolor = color.white)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Plot previous day high and low levels for reference
plot(stored_prev_high, "Previous Day High", color=color.new(color.yellow, 0), linewidth=1, style=plot.style_line)
plot(stored_prev_low, "Previous Day Low", color=color.new(color.yellow, 0), linewidth=1, style=plot.style_line)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Alert when price reaches previous day high or low
alertcondition(close >= stored_prev_high, "Price reached Previous Day High", "Price has reached the Previous Day High level")
alertcondition(close <= stored_prev_low, "Price reached Previous Day Low", "Price has reached the Previous Day Low level")
